
include  $(RTOS_PATH)/base.make

ifeq (${CONFIG_CPU_FLOAT}, hard)
	L_KBSPR_BASIC_NAME = libkbspr_basic.a
else
	L_KBSPR_BASIC_NAME = libkbspr_basic_soft.a
endif


SUBDIRS := ./arch/src ./drivers/src ./drivers/src/common ./drivers/src/gtimer

CFLAGS := $(CFLAGS) -D$(BOARD_TYPE)
CFLAGS := $(CFLAGS) -I$(BOARD_MAKEFILE_PATH)/resident/basic/drivers/include  -I$(BOARD_MAKEFILE_PATH)/resident/basic/arch/include

VPATH = ${SRC_PATHS}

all: _subdir_make $(L_KBSPR_BASIC_NAME)

$(L_KBSPR_BASIC_NAME):
	rm -rf  *.a
	$(AR) $(AR_FLAGS) $@ @prjObjs.lst		

clean: _subdir_clean
	rm -rf  *.o
	rm -rf  *.a
	rm -rf  prjObjs.lst	

include $(RTOS_PATH)/Rules.make