include  $(RTOS_PATH)/base.make

SRC_PATHS = $(BOARD_MAKEFILE_PATH)/loaded/src/arch/src/xlat_tables_v2
SRC_FILES = ${wildcard $(patsubst %, %/*.c, $(SRC_PATHS))} ${wildcard $(patsubst %, %/*.S, $(SRC_PATHS))} 

SRC_OBJS = $(patsubst %.S, %.o, $(patsubst %.c, %.o, $(SRC_FILES)))

EXTRA_CFLAGS = $(DEFAULT_SEARCH_PATH)

CFLAGS := $(CFLAGS) -D$(BOARD_TYPE)
CFLAGS += -I$(BOARD_MAKEFILE_PATH)/loaded/include -I$(BOARD_MAKEFILE_PATH)/loaded/src/drivers/include -I$(BOARD_MAKEFILE_PATH)/loaded/src/arch/include

VPATH = ${SRC_PATHS}


all: ${SRC_OBJS}
	for L_OBJ in $(SRC_OBJS); do $(BIN_PATH)/echo $$L_OBJ >> $$BOARD_MAKEFILE_PATH/loaded/prjObjs.lst;	done	
	
clean:
	rm -rf  *.o

include $(RTOS_PATH)/Rules.make
