#include  $(RTOS_PATH)/base.make
SRC_PATHS = .
SRC_FILES =   $(notdir ${wildcard $(patsubst %, %/*.c, $(SRC_PATHS))}\
            ${wildcard $(patsubst %, %/*.S, $(SRC_PATHS))} )

SRC_OBJS = $(patsubst %.S, %.o, $(patsubst %.c, %.o, $(SRC_FILES)))

EXTRA_CFLAGS =


LDFLAGS := $(LDFLAGS) -r 
#COMPILE_COMMAND是在对应项目subdir.mk中定义的。
%o:%c
	$(COMPILE_COMMAND) $@ $<
	
%.o: %.S
	$(COMPILE_COMMAND) $@ $<

all: crt1.o 
	${LD} ${LDFLAGS} -o crt0.o   $^
 	
clean:
	rm -rf ${SRC_OBJS}


#include $(RTOS_PATH)/Rules.make

