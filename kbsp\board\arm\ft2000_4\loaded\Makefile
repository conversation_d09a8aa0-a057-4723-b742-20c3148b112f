include  $(RTOS_PATH)/base.make

ifeq (${CONFIG_CPU_FLOAT}, hard)
	L_KBSPL_NAME = libkbspl.a
else
	L_KBSPL_NAME = libkbspl_soft.a
endif


SUBDIRS := ./src ./src/arch/src ./src/arch/src/xlat_tables_v2 ./src/arch/src/xlat_tables_v2/aarch32 ./src/drivers/src 

CFLAGS := $(CFLAGS) -D$(BOARD_TYPE)
CFLAGS := $(CFLAGS) -I$(BOARD_MAKEFILE_PATH)/loaded/include  -I$(BOARD_MAKEFILE_PATH)/loaded/src/arch/include -I$(BOARD_MAKEFILE_PATH)/loaded/src/drivers/include -I$(BOARD_MAKEFILE_PATH)/loaded/src/drivers/include/xlat_tables -I$(BOARD_MAKEFILE_PATH)/loaded/src/drivers/include/xlat_tables/aarch32 -I$(BOARD_MAKEFILE_PATH)/loaded/src/drivers/include/xlat_tables/aarch64

VPATH = ${SRC_PATHS}

all: _subdir_make $(L_KBSPL_NAME)

$(L_KBSPL_NAME):
	rm -rf  *.a	
	$(AR) $(AR_FLAGS) $@ @prjObjs.lst			
	rm -rf *.o

clean: _subdir_clean
	rm -rf  *.o
	rm -rf  *.a
	rm -rf  prjObjs.lst	

include $(RTOS_PATH)/Rules.make