
/*
 * @file: printk.c
 * @brief:
 *     <li> 实现打印输出接口。</li>
 * @implements: 
 */

/************************头 文 件******************************/
#include <vmkTypes.h>
#include <stdarg.h>
#include <stdio.h>

#include <mathBase.h>
#include <floatio.h>
//#include <vbsp.h>
/************************宏 定 义******************************/
#define INT_MIN 0x80000000

/************************类型定义******************************/
outputfunc_ptr      outputChar;

#ifdef CONFIG_CORE_SMP
#ifdef TTOS_RUN_IN_USER
    typedef void (*useroutputfunc_ptr)(T_BYTE *fmt, va_list *ap);
#endif
#endif

/************************外部声明******************************/
T_EXTERN T_VOID kprintkWithValist(T_BYTE *fmt, va_list ap);
/************************前向声明******************************/
/************************模块变量******************************/
/************************全局变量******************************/
/************************实    现******************************/

/*
 * @brief
 *    输出64位长度整型值。
 * @param[in]: num: 打印数值。
 * @param[in]: base: 打印数字的类型。
 * @param[in]: sign:  打印数字是否为有符号，1表示有符号，0表示无符号。
 * @return:
 *    无
 * @implements: DINIT.19
 */
static void printLongLongNum(
    signed long long num,
    unsigned int     base,
    unsigned int     sign)
{
    unsigned long long 	n = 0, num2 = 0;
    unsigned char 	   	count = 0,i = 0;
    unsigned char      	toPrint[64] = {0};

    /* @REPLACE_BRACKET: (sign == 1) && (num <  0) */
    if ((sign == U(1)) && (num < LL(0)))
    {
        /* @KEEP_COMMENT: 调用outputChar()输出负号'-' */
        outputChar('-');

        /* @KEEP_CODE: */
        num2 = (unsigned long long)(-num);
    }
    else
    {
        /* @KEEP_CODE: */
        num2 = (unsigned long long)num;
    }

    count = U(0);

    /* @KEEP_CODE: */
    n = num2 / base;

    /* @REPLACE_BRACKET: n > 0 */
    while (n > ULL(0))
    {
        /* @KEEP_COMMENT: 记录下num2除以base的余数 */
        toPrint[count] = (unsigned char)(num2 - (n * base));
        count          = count + U(1);
        /* @KEEP_CODE: */
        num2 = n;
        /* @KEEP_CODE: */
        n = num2 / base;
    }

    toPrint[count] = (unsigned char)num2;

    /* @REPLACE_BRACKET: 循环变量i = 0;i < count; i++*/
    for (i = 0;i <= count; i++)
    {
        /* @KEEP_COMMENT: 调用outputChar()逆向输出之前记录的余数 */
        outputChar((char)("0123456789abcdef"[(unsigned char)(toPrint[count - i ])]));
    }
}

/*
 * @brief:
 *    以32位的形式按照<base>和<sign>指定的格式打印数字<num>。
 * @param[in] num: 打印数字个数。
 * @param[in] base: 打印数字的类型。
 * @param[in] sign:  打印数字是否为有符号，1表示有符号，0表示无符号。
 * @return: 
 *    无
 */
T_MODULE T_VOID printNum(T_UWORD num,T_UWORD base,T_WORD sign)
{
    T_UWORD n = 0;
    T_UWORD count = 0;
    T_UWORD toPrint[20] = {0};

    if ((sign == 1) && (INT_MIN == (num & INT_MIN)))
    {
        /* 输出负号'-' */
        outputChar('-');

        /* 设置num为正数 */
        num = ((~num) + 1);
    }

    /* 计算num/base保存至变量n */
    count = 0;
    n = num / base;

    while (n > 0)
    {
        toPrint[count] = (num - (n * base));

        count++;
        num = n;
        n = num / base;
    }

    /* num保存至数组toPrint */
    toPrint[count] = num;
    count++;

    for (n = 0;n < count;n++)
    {
        /* 逆向输出数组中的字符 */
        outputChar("0123456789ABCDEF"[toPrint[count - (n + 1)]]);
    }
}

T_VOID printkWithvalist(T_BYTE *fmt, va_list ap)
{
    char c = 0;
    char *str = NULL;
    T_WORD sign = 0;
    T_UWORD base = 0;
    T_BOOL isLongLong = FALSE;

    /* 格式字符串读取; <*fmt>不为'\0'; <fmt>++ */
    for (;'\0' != (*fmt);fmt++)
    {
        /* 设置数字的类型base为0，设置是否有符号sign为0 */
        base = 0;
        sign = 0;


        /* <*fmt>为'%' */
        if ('%' == *fmt)
        {
            /* ++<fmt>，设置转换字符c为<*fmt> */
            ++fmt;
            c = *fmt;

            /* <*fmt>为'l' */
            if ('l' == *fmt)
            {
                /* ++<fmt>，设置转换字符c为<*fmt> */
                ++fmt;
                c = *fmt;
                /* @REPLACE_BRACKET: 再下一个字符为'l' */
                if (c == 'l')
                {
                    /* @KEEP_COMMENT: 标记打印格式是long long类型 */
                    isLongLong = TRUE;
                    fmt++;
                    c = *fmt;
                }
            }

            /* c */
            switch (c)
            {
                case 'o':

                    /* 设置进制字符base为8，设置符号sign为0 */
                    base = 8;
                    sign = 0;

                    break;

                case 'O':

                    /* 设置进制字符base为8，设置符号sign为0 */
                    base = 8;
                    sign = 0;

                    break;

                case 'd':

                    /* 设置进制字符base为10，设置符号sign为1 */
                    base = 10;
                    sign = 1;

                    break;

                case 'D':

                    /* 设置进制字符base为10，设置符号sign为1 */
                    base = 10;
                    sign = 1;

                    break;

                case 'u':

                    /* 设置进制字符base为10，设置符号sign为0 */
                    base = 10;
                    sign = 0;

                    break;

                case 'U':

                    /* 设置进制字符base为10，设置符号sign为0 */
                    base = 10;
                    sign = 0;

                    break;

                case 'x':

                    /* 设置进制字符base为16，设置符号sign为0 */
                    base = 16;
                    sign = 0;

                    break;

                case 'X':

                    /* 设置进制字符base为16，设置符号sign为0 */
                    base = 16;
                    sign = 0;

                    break;

                case 's':

                    /*
                     * 可变参数列表中指向字符串的指针到str; 
                     * *str不为'\0'; str++
                     */
                    for (str = va_arg(ap,char *);'\0' != *str;str++)
                    {
                        /* 输出str字符串中每一个字符 */
                        outputChar(*str);
                    }

                    break;

                case 'c':

                    /* 输出可变参数列表中指向无符号整形的字符 */
                    outputChar(va_arg(ap,T_UWORD));

                    break;

                default:

                    /* 输出转换字符c的每一个字符 */
                    outputChar(c);

                    break;
            }

            /* base不为0 */
            if (0 != base)
            {
            	 /* @REPLACE_BRACKET: 待输出数据是long long类型 */
				if (isLongLong != FALSE)
				{
					/* @KEEP_COMMENT: 调用printLongLongNum()打印数据 */
					printLongLongNum(va_arg(ap, signed long long), base, sign);
					/* @KEEP_COMMENT: 取消long long类型标记 */
					isLongLong = FALSE;
				}
				else
				{
					/* @KEEP_COMMENT: 调用printNum()打印数据 */
					printNum(va_arg(ap, int), base, (T_WORD)sign);
				}
            }
        }
        else
        {
            /* 输出格式字符串中每一个字符 */
            outputChar(*fmt);
        }
    }
}

/**
 * @brief:
 *    将可变参数列表中的参数按照<fmt>指定的格式进行格式化，并把格式化后的数据输
 *    出到指定设备。
 * @param[in]: fmt: 格式字符串
 * @param[in]: ...: 可变参数列表
 * @return:
 *    无
 * @notes:
 *    格式字符串由两种类型的对象组成：
 *        普通字符。被原封不动输出到标准输出中；
 *        转换说明。每个转换说明都以百分号字符‘%’作为起始符，后面紧跟转换字符。
 *    格式字符串中转换字符用来说明输出转换的类型，转换字符及其输出类型如下所示：
 *        d或D: 输出一个有符号十进制整数。
 *        u或U: 输出一个无符号十进制整数。
 *        o或O: 输出一个八进制整数。
 *        x或X: 输出一个十六进制整数。
 *        s: 输出字符串，直到遇到字符串结束符'\0'为止。
 *        c: 输出一个单字符。
 *    如果百分号字符与转换字符之间为字母l时，则将输出的整数作为长整型输出。
 *    如果百分号字符后面的字符不在上述转换字符范围内，则转换字符作为普通字符输出。
 *    如果参数的个数（在参数表中“...”部分填入的参数）少于<fmt>参数中指定的格
 *    式的个数，调用本接口会导致错误的数据输出。
 *    如果参数的个数（在参数表中“...”部分填入的参数）超过了<fmt>参数中指定的
 *    格式的个数，则多余的参数被忽略。
 */
T_VOID printk(const char *fmt, ...)
{
    va_list ap = {};
    /* 开始可变参数读取 */
    va_start(ap,fmt);

#ifdef CONFIG_CORE_SMP
#ifdef TTOS_RUN_IN_USER
    (*((useroutputfunc_ptr)outputChar))((T_BYTE *)fmt,&ap);
#else
    kprintkWithValist((T_BYTE *)fmt,ap);
#endif
#else
    printkWithvalist(fmt,ap);
#endif

    /* 结束可变参数读取 */
    va_end(ap);
}

/**
 * @brief:
 *    初始化打印单个字符的函数指针。
 * @param[in]: printChar: 单个字符的函数指针
 * @return:
 *    无
 */
T_VOID printkInit(outputfunc_ptr printChar)
{
    /* outputChar = printChar */
    outputChar = printChar;
}
