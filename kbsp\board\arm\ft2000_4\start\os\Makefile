include  $(CONFIG_PATH)/make/v_makefile.mk
include  $(RTOS_PATH)/base.make
SRC_PATHS = .
SRC_FILES =   $(notdir ${wildcard $(patsubst %, %/*.c, $(SRC_PATHS))}\
            ${wildcard $(patsubst %, %/*.S, $(SRC_PATHS))} )

SRC_OBJS = $(patsubst %.S, %.o, $(patsubst %.c, %.o, $(SRC_FILES))) *.o

EXTRA_CFLAGS = $(DEFAULT_SEARCH_PATH)

BASE_CFLAGS_CRT0 := ${COMPILE_DEBUG} ${COMPILE_WARNING} ${COMPILE_OPTIMIZATION} 
BASE_CFLAGS_CRT0 := $(BASE_CFLAGS_CRT0) $(CPU_FLAGS) -D$(BOARD_MACRO) 
CFLAGS_CRT0 := $(BASE_CFLAGS_CRT0)  $(CPU_ENDIAN_CFLAGS) $(CPU_FLOAT_CFLAGS)


LDFLAGS := $(LDFLAGS) -r 

#crt1.o  board.o 鏄�杩嘡ules.make缂栬瘧鍑烘潵鐨勩�
#鐢变簬lmain.c闇�瀹氫箟涓嶅悓鐨勫畯缂栬瘧鍑轰袱涓洰鏍囨枃浠讹紝鎵�互姝ゅ鍚姩浠ｇ爜鐨勭紪璇戣鍒欎笌boot鍚姩浠ｇ爜(閫氳繃鍦ㄥ搴旈」鐩畇ubdir.mk涓畾涔夌殑COMPILE_COMMAND鏉ョ紪璇�涓嶅悓銆�

all: crt1.o  board.o 
	$(CC) -fno-builtin  -Wstrict-aliasing $(CFLAGS_CRT0) $(EXTRA_CFLAGS) $(CFLAGS_$@) $(VERSION_FLAGS) -c -o lmain.o ${SRC_PATHS}/lmain.c
	$(CC) -fno-builtin  -Wstrict-aliasing $(CFLAGS_CRT0) -D_SEP_ $(EXTRA_CFLAGS) $(CFLAGS_$@) $(VERSION_FLAGS) -c -o lmain_sep.o ${SRC_PATHS}/lmain.c
	${LD} ${LDFLAGS} -o crt0.o   $^ lmain.o
	${LD} ${LDFLAGS} -o crt0_sep.o   $^ lmain_sep.o
	rm -rf crt1.o board.o vmBind.o lmain.o lmain_sep.o 

clean:
	rm -rf ${SRC_OBJS}


include $(RTOS_PATH)/Rules.make

