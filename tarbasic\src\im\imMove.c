

/*
 * @file:imMove.c
 * @brief:
 *             <li>映像搬移实现</li>
 * @implements: DTA.1.4.5   DTA.1.4.6   DTA.1.4.7
 */

/* @<MODULE     */
/************************头 文 件******************************/

/* @<MOD_HEAD 	*/
#include <kbsp.h>
#include "dbMemory.h"
#include "imAccess.h"


/* @MOD_HEAD> 	*/

/************************宏 定 义******************************/
/************************类型定义******************************/
/************************外部声明******************************/
/************************前向声明******************************/
/************************模块变量******************************/
/************************全局变量******************************/
/************************实   现*******************************/
/************************模块实现*******************************/
/* @MODULE> */

/*
 * @brief:
 *      将指定段移动到指定地址
 * @param[in]: imgSegment: 指向保存段信息指针
 * @param[in]: segmentVerifyStart: 段校验值存放起始地址
 * @param[in]: verifyTag: 是否需要校验的标示
 * @param[in]: vmID: 分区ID
 * @return:
 *      TA_OK: 搬移成功
 *      TA_IM_VERIFY_FAIL: 校验失败
 *      TA_INVALID_ALIGN_SIZE:非法的硬件访问对齐宽度
 *      TA_FAIL: 读写内存失败
 * @tracedREQ: RTA.2
 * @implements: DTA.1.4.5
 */
T_MODULE T_TA_ReturnCode taIMMoveSegmentToMemory(UINT32 vmID,
T_TA_IMSegmentDescription* imgSegment, 
UINT32 segmentVerifyStart, 
BOOL verifyTag)
{
    UINT32 srcAddr = imgSegment->storeAddr;
    UINT32 desAddr = 0;
    UINT32 segmentSize = imgSegment->size;
    T_DB_AccessMemoryAlign alignSize = Align_None;
    T_TA_ReturnCode result = TA_OK;
    T_BOOL isMMUEnabled = FALSE;
    //TA_IS_MMUEnabled(isMMUEnabled);

    /* @REPLACE_BRACKET: MMU已经使能 */
    if (TRUE == isMMUEnabled)
    {
        /* 运行时搬移分区映像时走到这里 */
        /* @KEEP_COMMENT: 设置装载目的地址为段逻辑运行地址 */
        desAddr = imgSegment->logicRunAddr;
    }
    else
    {
        /* @KEEP_COMMENT: 设置装载目的地址为装载地址 */
        desAddr = imgSegment->loadAddr;
    }

    /* @REPLACE_BRACKET: 源地址和目的地址相同,无需搬移 */
    if (desAddr == srcAddr)
    {
        /* @REPLACE_BRACKET: TA_OK */
        return(TA_OK);
    }

    /* 获取源地址硬件访问宽度，因为源一般为Flash，可能有访问宽度要求*/
    /* @KEEP_COMMENT: 获取源地址硬件访问宽度 */
    result = taGetOperateSize(srcAddr
    , segmentSize
    ,0xFFFFFFFF
    ,&alignSize); 

    /* @REPLACE_BRACKET: 如果访问宽度错误 */
    if (TA_OK != result)
    {
        /* @KEEP_COMMENT: 返回错误码 */
        return(result);
    }

    /* 这里设置为强制写内存主要是因为考虑到目的空间可能被设置了部分保护的情况(比如：代码段保护) */
    /* @KEEP_COMMENT: 拷贝到目的位置 */
    result = taWriteMemory(vmID,desAddr,(const UINT8*)srcAddr,segmentSize,alignSize,TRUE);

    /* @REPLACE_BRACKET: 如果拷贝失败 */
    if (TA_OK != result)
    {
        /* @REPLACE_BRACKET: TA_FAIL */
        return(TA_FAIL);
    }

    /* @REPLACE_BRACKET: 需要进行校验 */
    if (TRUE == verifyTag)
    {
        /* @KEEP_COMMENT: 通过外部提供的校验Hook接口进行校验，如果失败返回IM_VERIFY_FAIL */
        result = taIMVerifyAndCorrectDataHook(imgSegment->size,segmentVerifyStart,imgSegment->loadAddr);      
    }

    /* @REPLACE_BRACKET: 校验失败 */
    if (TA_OK != result)
    {
        /* @REPLACE_BRACKET: TA_IM_VERIFY_FAIL */
        return(TA_IM_VERIFY_FAIL);
    }

    /* @REPLACE_BRACKET: TA_OK */
    return(TA_OK);
}

/*
 * @brief:
 *      将指定映像移动到指定地址
 * @param[in]: flashConfig: 指向映像空间信息的指针
 * @param[in]: ImgSegment: 指向映像段信息的指针
 * @param[in]: imageHead: 指向映像头信息的指针
 * @param[in]: tVerifyTag: 是否校验标示
 * @param[in]: imageSize: 映像大小
 * @param[in]: vmID: 分区ID
 * @return:
 *      TA_OK: 搬移成功
 *      TA_IM_LOAD_ADDR_IN_FLASH:映像加载地址在Flash中
 *      TA_IM_VERIFY_FAIL: 校验失败 * 		
 *      TA_INVALID_PARAM: 对齐宽度不合法
 *      TA_FAIL: 读写内存失败
 * @tracedREQ: RTA.2
 * @implements: DTA.1.4.6
 */
static T_TA_ReturnCode IMInternalMoveImage(UINT32 vmID,
T_TA_IMFlashConfigInfo* flashConfig,
T_TA_IMSegmentDescription* imgSegment,
T_TA_ImageInfoHead*  imageHead,
BOOL verifyTag,
UINT32 imageSize)
{
    T_TA_ReturnCode ret = TA_OK;
    INT32 i = 0;
    UINT32 segVerifyStartAddr = 0;
    T_TA_IMSegmentDescription *pTmpSeg = NULL;
    UINT32 count = 0;

    /* @REPLACE_BRACKET: 如果为不需要搬移的映像 */
    if (( BURNAREA_TYPE_OS != imageHead->imageType )
    &&  ( BURNAREA_TYPE_PARTITION != imageHead->imageType ))
    {
        /* @REPLACE_BRACKET: TA_OK */
        return(TA_OK);
    }

    /* 
     * 依次检查映像的段，如果该段是需要搬移的段，
     * 并且通过imIsAddrInFlash检查该段的装载地址在配置的映像存储空间中，
     * 则返回TA_IM_LOAD_ADDR_IN_FLASH
     */
    /* @REPLACE_BRACKET: 依次检查映像的段  */
    for (i = 0; i < imageHead->segNum; i++)
    {
        /* @REPLACE_BRACKET: 要搬移，但搬移目的地址在Flash中 */
        if ((TRUE == (imgSegment + i)->isLoad)
        &&  (TRUE == taIMIsAddrInFlash((imgSegment+i)->loadAddr)))
        {
            /* @REPLACE_BRACKET: TA_IM_LOAD_ADDR_IN_FLASH */
            return(TA_IM_LOAD_ADDR_IN_FLASH);
        }
    }

    /* @REPLACE_BRACKET: 依次搬移和校验所有段  */
    for (i = 0; i < imageHead->segNum; i++)
    {
        pTmpSeg = imgSegment + i;           
        segVerifyStartAddr = (flashConfig->segVerifyConfig + i)->segVerifyStart;

        /* @REPLACE_BRACKET: 该段存在于bin文件中 */
        if ((count < imageSize) && (pTmpSeg->size > 0))
        {
            count += pTmpSeg->size;

            /* @REPLACE_BRACKET:要搬移到内存*/
            if (TRUE == pTmpSeg->isLoad)
            {
                ret = taIMMoveSegmentToMemory(vmID,pTmpSeg, segVerifyStartAddr, verifyTag);
                /* @REPLACE_BRACKET:搬移到内存并校验(如果需要)失败 */
                if (TA_OK != ret)
                {
                    /* @KEEP_COMMENT:设置错误码到ret */
                    break;
                }
            }
            else
            {
                /* @REPLACE_BRACKET:如果要校验*/                
                if (TRUE == verifyTag)
                {
                    ret = taIMVerifyAndCorrectDataHook(pTmpSeg->size,segVerifyStartAddr,pTmpSeg->storeAddr);        
                    /* @REPLACE_BRACKET:校验数据失败 */        
                    if (TA_OK != ret)
                    {
                        /* @KEEP_COMMENT:设置错误码为TA_IM_VERIFY_FAIL */
                        ret = TA_IM_VERIFY_FAIL;
                        break;
                    }
                }
            }
        }
    }

    /* @KEEP_COMMENT: 搬移成功,返回TA_OK */
    /* @KEEP_COMMENT: 搬移失败,返回ret中的错误码 */
    return(ret);
}

/************************全局实现*******************************/

/*
 * @brief:
 *      搬移指定映像，解析映像头信息，将映像中需要搬移的段搬移到指定位置
 * @param[in]: imageInfo: 指向映像信息的指针
 * @param[in]: imgSegment: 指向保存段信息的指针
 * @param[in]: head: 指向指向无段信息映像头的指针
 * @param[in]: isNeedVerify: 是否需要校验的标示
 * @param[in]: vmID: 分区ID
 * @return:
 *      TA_OK:搬移成功
 *      TA_INVALID_INPUT:非法的输入参数
 *      TA_IM_IMAGE_PARSER_FAIL:解析映像格式失败
 *      TA_IM_SEG_INFO_ERROR:检查段信息失败
 *      TA_IM_VERIFY_FAIL:校验失败
 *      TA_IM_LOAD_ADDR_IN_FLASH:映像加载地址在Flash中
 *      TA_INVALID_ALIGN_SIZE:非法的硬件访问对齐宽度
 *      TA_FAIL: 读写内存失败
 * 		
 * @tracedREQ: RTA.2
 * @implements: DTA.1.4.7
 */
T_TA_ReturnCode taIMMoveImage(UINT32 vmID,
T_TA_IMFlashConfigInfo *imageInfo,
T_TA_IMSegmentDescription* imgSegment,
T_TA_ImageInfoHead* head,
BOOL isNeedVerify)
{
    T_TA_ReturnCode returnCode = TA_OK;
    UINT32 startAddr = 0;
    BOOL isSegmentVerify = FALSE;/* 分段来校验 */
    UINT32 i = 0; /* 循环变量 */
    T_TA_IMAdditionalInfo taAddInfo;
    T_TA_ImageInfoHead imageHead;/* 映像头 */
    T_DB_AccessMemoryAlign alignSize;
    T_BOOL isMMUEnabled = FALSE;

    memset((void *)(&taAddInfo),0,sizeof(taAddInfo));

    /* 注意：
     *  这里假设了imgSegmemt的空间一定能装下所有的段
     * */

    /* 外部接口，需判空 */
    /* @REPLACE_BRACKET: 指针参数存在空指针 */
    if ((NULL == imageInfo)
    ||  (NULL == imgSegment)
    ||  (NULL == head))
    {
        /* @REPLACE_BRACKET: TA_INVALID_INPUT */
        return(TA_INVALID_INPUT);
    }

    /* 解析映像，获取段信息 */
    /* @KEEP_COMMENT: 解析映像，获取映像头信息 */
    returnCode = taIMParseImage(vmID,imageInfo, imgSegment, head);

    /* @REPLACE_BRACKET: 解析映像失败 */
    if (TA_OK != returnCode)
    {
        /* @REPLACE_BRACKET: TA_IM_IMAGE_PARSER_FAIL */
        return(TA_IM_IMAGE_PARSER_FAIL);
    }

    returnCode = taIMCheckSegmentSpaceInfo(imageInfo->start, imageInfo->size, imgSegment, head->segNum);
    /* @REPLACE_BRACKET: 检查映像中段合法性,是否在有效范围内,如果不合法 */
    if (TA_OK != returnCode)
    {
        /* @REPLACE_BRACKET: TA_IM_SEG_INFO_ERROR */
        return(TA_IM_SEG_INFO_ERROR);
    }

    /* @KEEP_COMMENT: 获取硬件访问宽度 */
    returnCode = taGetOperateSize(imageInfo->imageInfoAddrStart
    , sizeof(taAddInfo)
    ,0xFFFFFFFF
    ,&alignSize); 

    /* @REPLACE_BRACKET: 如果访问宽度错误 */
    if (TA_OK != returnCode)
    {
        /* @KEEP_COMMENT: 返回错误码 */
        return(returnCode);
    }

    /* 读映像附加信息 */
    taReadMemory(vmID,imageInfo->imageInfoAddrStart,(UINT8*)&taAddInfo,sizeof(taAddInfo), alignSize,FALSE);

    /* 下述判断，增加块数判断，块数要求为等于0 ，确保是按段校验*/
    /* @REPLACE_BRACKET: 如果需要进行分段校验*/
    if ((TRUE == isNeedVerify) && (0 == imageInfo->blockNum))
    {
        /* @KEEP_COMMENT: 计算各个段需要的校验区域大小 */
        taIMCalcSegmentVerifyInfo(imageInfo, imgSegment, head->segNum,taAddInfo.imgSize);
        isSegmentVerify = TRUE;
    }

	/* 搬映像 */
    returnCode = IMInternalMoveImage(vmID,imageInfo, imgSegment, head, isSegmentVerify,taAddInfo.imgSize); 
    
    /* @REPLACE_BRACKET: 搬移和校验映像(如果需要校验)出错 */
    if (TA_OK != returnCode)
    {
        /* @KEEP_COMMENT: 返回错误码 */
        return(returnCode);
    }

	/* 只有搬完了所有段，才可能进行块校验，所以块校验放到最后 */
    /* @REPLACE_BRACKET:如果需要进行分块校验 */
    if ((TRUE == isNeedVerify) && (imageInfo->blockNum != 0))
    {	    
	    //TA_IS_MMUEnabled(isMMUEnabled);
	
	    /* @REPLACE_BRACKET: 如果MMU已经使能 */
	    if (TRUE == isMMUEnabled)
	    {
	        /* @KEEP_COMMENT: 校验读数据时使用逻辑起始地址 */
            startAddr = imgSegment[0].logicRunAddr;
	    }
	    else
	    {
	        /* @KEEP_COMMENT: 校验读数据时使用物理加载地址 */
            startAddr = imgSegment[0].loadAddr;
	    }
    
        /* @KEEP_COMMENT: 调用taIMGenerateImageInfoHeadWithBlock按分割块数构造段信息 */
        taIMGenerateImageInfoHeadWithBlock(imageInfo->start, imageInfo->blockNum, taAddInfo.imgSize, imgSegment, &imageHead);

        /* @KEEP_COMMENT: 调用taIMCalcSegmentVerifyInfo计算映像各段的校验码 */
        taIMCalcSegmentVerifyInfo(imageInfo, imgSegment, imageHead.segNum,taAddInfo.imgSize);

        /* 注:这里的segNum不再是映像头的segNum了,而是块数量 */
        /* @REPLACE_BRACKET: 遍历所有的段 */
        for (i = 0; i < imageHead.segNum; i++)
        {
            returnCode = taIMVerifyAndCorrectDataHook((imgSegment + i)->size, imageInfo->segVerifyConfig[i].segVerifyStart, startAddr+(imgSegment->size * i));
            /* @REPLACE_BRACKET: 对块进行校验失败 */
            if (returnCode != TA_OK)
            {
                /* @REPLACE_BRACKET: TA_IM_VERIFY_FAIL */
                return(TA_IM_VERIFY_FAIL);
            }
        }
    }

    /* @KEEP_COMMENT: 搬移和校验映像(如果需要校验)成功,返回TA_OK */
    return(TA_OK);
}

