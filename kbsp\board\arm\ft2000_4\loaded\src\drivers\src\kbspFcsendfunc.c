#include <string.h>
#include "kbspFcae.h"

#define JH_FC_SOF_POS       (0)
#define JH_FC_RCTL_POS      (4)
#define JH_FC_DID_POS       (5)
#define JH_FC_CSCTL_POS     (8)
#define JH_FC_SID_POS       (9)
#define JH_FC_TYPE_POS      (12)
#define JH_FC_FCTL_POS      (13)
#define JH_FC_SEQID_POS     (16)
#define JH_FC_DFCTL_POS     (17)
#define JH_FC_SEQCNT_POS    (18)
#define JH_FC_OXID_POS      (20)
#define JH_FC_RXID_POS      (22)
#define JH_FC_PARA_POS      (24)
#define JH_FC_MESGID_POS    (28)
#define JH_FC_VEDSS_POS     (32)
#define JH_FC_RES_POS       (36)
#define JH_FC_PRI_POS       (40)
#define JH_FC_LEN_POS       (41)


static int jh_set_sof(unsigned char *pkt)
{
    static unsigned char sof[4] = {0xBC,0xB5,0x56,0x56};
    memcpy(pkt + JH_FC_SOF_POS,sof,sizeof(sof));
    return 1;
}

static int jh_set_eof(unsigned char *pkt, int align_len)
{
    static unsigned char eof[4] = {0xBC,0x95,0x75,0x75};
    memcpy((pkt + FC_CRC_LEN +align_len),eof,sizeof(eof));
    return 1;
}

static int jh_set_crc(unsigned char *pkt, int align_len)
{
    static unsigned char crc[4] = {0};
    memcpy((pkt + align_len),crc,sizeof(crc));
    return 1;
}

/***************************FC-AE HEADER**************************/
static int jh_set_rctl(unsigned char *pPkt)
{
    static unsigned char rctl[1] = {0x04}; /*fixed para*/
    memcpy(pPkt + JH_FC_RCTL_POS, rctl, sizeof(rctl));
    return 1;
}

static int jh_set_did(unsigned char *pPkt, JhFcDeviceID nDid)
{
    memcpy(pPkt + JH_FC_DID_POS, nDid.nId, sizeof(JhFcDeviceID));
    return 1;
}

static int jh_set_csctl(unsigned char *pPkt)
{
    static unsigned char csctl[1] = {0x00}; /*service type III*/
    memcpy(pPkt + JH_FC_CSCTL_POS, csctl, sizeof(csctl));
    return 1;
}

static int jh_set_sid(unsigned char *pPkt,JhFcDeviceID nSid)
{
    memcpy(pPkt + JH_FC_SID_POS, nSid.nId, sizeof(JhFcDeviceID));
    return 1;
}

static int jh_set_type(unsigned char *pPkt)
{
    static unsigned char type[1] = {0x49}; /*ASM frame*/
    memcpy(pPkt + JH_FC_TYPE_POS, type, sizeof(type));
    return 1;
}

static int jh_set_fctl(unsigned char *pPkt, int len)
{
    static unsigned char fctl[3] = {0x38,0x00,0x00};
    memcpy(pPkt + JH_FC_FCTL_POS, fctl, sizeof(fctl));
    return 1;
}

static int jh_set_seqid(unsigned char *pPkt)
{
    static unsigned char seqid[1] = {0};
    memcpy(pPkt + JH_FC_SEQID_POS, seqid, sizeof(seqid));
    return 1;
}

static int jh_set_dfctl(unsigned char *pPkt)
{
    static unsigned char dfctl[1] = {0};
    memcpy(pPkt + JH_FC_DFCTL_POS, dfctl, sizeof(dfctl));
    return 1;
}

static int jh_set_seqcnt(unsigned char *pPkt)
{
    static unsigned char seqcnt[2] = {0};
    memcpy(pPkt + JH_FC_SEQCNT_POS, seqcnt, sizeof(seqcnt));
    return 1;
}

static int jh_set_oxid(unsigned char *pPkt, unsigned int oxid)
{
    JhFcOxID n_oxid;
    int i;
    for(i = 0; i < 2; i++){
        n_oxid.nId[i] = (((oxid) >> ((1 - i) * 8)) & 0xff);
    }
    memcpy(pPkt + JH_FC_OXID_POS, n_oxid.nId, sizeof(JhFcOxID));
    return 1;
}

static int jh_set_rxid(unsigned char *pPkt)
{
    static unsigned char rxid[2] = {0};
    memcpy(pPkt + JH_FC_RXID_POS, rxid, sizeof(rxid));
    return 1;
}

static int jh_set_para(unsigned char *pPkt)
{
    static unsigned char para[4] = {0};
    memcpy(pPkt + JH_FC_PARA_POS, para, sizeof(para));
    return 1;
}

/***************************ASM HEADER**************************/
static int jh_set_msgid(unsigned char *pPkt, Jh_msg_id msgid)
{
    memcpy(pPkt + JH_FC_MESGID_POS, msgid.nId, sizeof(Jh_msg_id));
    return 1;
}

static int jh_set_vedss(unsigned char *pPkt)
{
    static unsigned char vedss[4] = {0}; /*ASM frame*/
    memcpy(pPkt + JH_FC_VEDSS_POS, vedss, sizeof(vedss));
    return 1;
}

static int jh_set_res(unsigned char *pPkt)
{
    static unsigned char res[4] = {0}; /*ASM frame*/
    memcpy(pPkt + JH_FC_RES_POS, res, sizeof(res));
    return 1;
}

static int jh_set_pri(unsigned char *pPkt)
{
    static unsigned char pri[1] = {0}; /*ASM frame*/
    memcpy(pPkt + JH_FC_PRI_POS, pri, sizeof(pri));
    return 1;
}

static int jh_set_len(unsigned char *pPkt, int payload_len)
{
    Jh_payload_len pl_len;
    int i;
    for(i = 0; i < 3; i++){
        pl_len.len[i] = (((payload_len) >> ((2 - i) * 8)) & 0xff);
    }
    memcpy(pPkt + JH_FC_LEN_POS, pl_len.len, sizeof(Jh_payload_len));
    return 1;
}

/*************************FRAMING********************************/
/* -----------------------------------------------
Function Name	:jh_send_frameing
Purpose			:construct FC frame
InPut			:device struct, data addr, d_id, s_id, msg_id, data length
OutPut			:
return			:success = 0, fail = -6
----------------------------------------------- */
int jh_send_frameing(T_UWORD oxid, unsigned char *pPkt, JhFcDeviceID nDid,
        JhFcDeviceID nSid, Jh_msg_id msgid, int len)
{
    int alig_len = 0;
    alig_len = len + FC_SOF_LEN + FC_AE_ASM_HEADER_LEN;
    alig_len = alig_len + ((alig_len % 4 == 0) ? 0 :(4 - alig_len % 4));//wp20180226: FC transfer 4B-alignment

    if (20 != (jh_set_sof(pPkt) + jh_set_rctl(pPkt) + jh_set_did(pPkt, nDid) +jh_set_csctl(pPkt)
        +jh_set_sid(pPkt, nSid) + jh_set_type(pPkt) + jh_set_fctl(pPkt, len) + jh_set_seqid(pPkt)
        +jh_set_dfctl(pPkt) + jh_set_seqcnt(pPkt) + jh_set_oxid(pPkt, oxid) + jh_set_rxid(pPkt)
        +jh_set_para(pPkt) + jh_set_msgid(pPkt, msgid) + jh_set_vedss(pPkt) + jh_set_res(pPkt)
       +jh_set_pri(pPkt) + jh_set_len(pPkt, len) + jh_set_crc(pPkt, alig_len) + jh_set_eof(pPkt, alig_len)))
    {
        return -1;
    } else {
        return 0;
    }
}
