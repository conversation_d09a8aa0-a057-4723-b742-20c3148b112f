
include  $(RTOS_PATH)/base.make

#CFLAGS := $(CFLAGS) -D$(BOARD_TYPE)
#CFLAGS_DEBUG = $(CFLAGS) -I$(BOARD_MAKEFILE_PATH)/resident/debug/include 

#直接编译debug及子目录下的所有文件，不会使用子目录下的makefile。

EXTRA_CFLAGS = $(DEFAULT_SEARCH_PATH)

all:
	${MAKE} "CFLAGS :=$(CFLAGS_DEBUG) " "L_OBJS=$(SRC_KBSPR_DEBUG_OBJS)" "L_NAME :=$(L_KBSPR_DEBUG_NAME)" \
	"VPATH = $(SRC_KBSPR_DEBUG_PATHS)" $(L_KBSPR_DEBUG_NAME) -f $(RTOS_PATH)/Rules.make
	${LD} -r -o $(L_KBSPR_DEBUG_NAME) $(SRC_KBSPR_DEBUG_OBJS)
#	cp $(L_KBSPR_DEBUG_NAME) $(BOARD_MAKEFILE_PATH)/lib/$(CONFIG_CPU_ENDIAN)
	$(RM) $(SRC_KBSPR_DEBUG_OBJS)
	$(RM) $(L_KBSPR_DEBUG_LIB_NAME) 

clean:
	rm -rf  *.o
	rm -rf  *.a

include $(RTOS_PATH)/Rules.make