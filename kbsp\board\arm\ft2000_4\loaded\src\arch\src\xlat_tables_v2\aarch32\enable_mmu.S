/*
 * Copyright (c) 2018, ARM Limited and Contributors. All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */
#define __ASSEMBLY__
#define ASM_USE
#include <kbspAsm.h>
#include <xlat_tables/xlat_tables_v2.h>

	.global	enable_mmu_direct_svc_mon
	.global	enable_mmu_direct_hyp

	/* void enable_mmu_direct_svc_mon(unsigned int flags, UINT64 *params) */
ENTRY(enable_mmu_direct_svc_mon)
	/* Assert that MMU is turned off */
#if ENABLE_ASSERTIONS
	ldcopr  r2, SCTLR
	tst	r2, #SCTLR_M_BIT
	ASM_ASSERT(eq)
#endif

	/* Invalidate TLB entries */
	TLB_INVALIDATE(r0, TLBIALL)

	mov	r3, r0
	mov	r0, r1

	/* MAIR0. Only the lower 32 bits are used. */
	ldr	r1, [r0, #(MMU_CFG_MAIR << 3)]
	stcopr	r1, MAIR0

	/* TTBCR. Only the lower 32 bits are used. */
	ldr	r2, [r0, #(MMU_CFG_TCR << 3)]
	stcopr	r2, TTBCR

	/* TTBR0 is unused right now; set it to 0. */
	ldr	r1, [r0, #(MMU_CFG_TTBR0 << 3)]
	ldr	r2, [r0, #((MMU_CFG_TTBR0 << 3) + 4)]
	stcopr16	r1, r2, TTBR0_64

	/* TTBR1 */
	ldr	r1, [r0, #(MMU_CFG_TTBR1 << 3)]
	ldr	r2, [r0, #((MMU_CFG_TTBR1 << 3) + 4)]
	stcopr16	r1, r2, TTBR1_64

	/*
	 * Ensure all translation table writes have drained into memory, the TLB
	 * invalidation is complete, and translation register writes are
	 * committed before enabling the MMU
	 */
	dsb	ish
	isb

	/* Enable enable MMU by honoring flags */
	ldcopr  r1, SCTLR
	//ldr	r2, =(SCTLR_WXN_BIT | SCTLR_C_BIT | SCTLR_M_BIT)
	ldr	r2, =(SCTLR_C_BIT | SCTLR_M_BIT)
	orr	r1, r1, r2

	/* Clear C bit if requested */
	tst	r3, #DISABLE_DCACHE
	bicne	r1, r1, #SCTLR_C_BIT

	stcopr	r1, SCTLR
	isb

	bx	lr
ENDPROC(enable_mmu_direct_svc_mon)
