

#ifndef KBSPRAWINTR_H_
#define KBSPRAWINTR_H_

#include "vmkTypes.h"


#ifdef FT_D2000
#define PLAT_GICD_BASE 0x29a00000
#define PLAT_GICR_BASE 0x29b00000
#else
#define PLAT_GICD_BASE 0x29900000
#define PLAT_GICR_BASE 0x29980000
#endif

#define GICR_BASE_OFFSET		U(0x20000)	/* 128 KB */


#define GICR_PCPUBASE_SHIFT	0x11
#define GICR_SGIBASE_OFFSET	U(65536)	/* 64 KB */
#define GICR_CTLR		U(0x0)
#define GICR_IIDR		U(0x04)
#define GICR_TYPER		U(0x08)
#define GICR_WAKER		U(0x14)
#define GICR_PROPBASER		U(0x70)
#define GICR_PENDBASER		U(0x78)

#define GICR_IGROUPR0		(GICR_SGIBASE_OFFSET + U(0x80))
#define GICR_ISENABLER0		(GICR_SGIBASE_OFFSET + U(0x100))
#define GICR_ICENABLER0		(GICR_SGIBASE_OFFSET + U(0x180))
#define GICR_ISPENDR0		(GICR_SGIBASE_OFFSET + U(0x200))
#define GICR_ICPENDR0		(GICR_SGIBASE_OFFSET + U(0x280))
#define GICR_ISACTIVER0		(GICR_SGIBASE_OFFSET + U(0x300))
#define GICR_ICACTIVER0		(GICR_SGIBASE_OFFSET + U(0x380))
#define GICR_IPRIORITYR		(GICR_SGIBASE_OFFSET + U(0x400))

#define GICR_ICFGR0			(GICR_SGIBASE_OFFSET + U(0xc00))
#define GICR_ICFGR1			(GICR_SGIBASE_OFFSET + U(0xc04))
#define GICR_IGRPMODR0		(GICR_SGIBASE_OFFSET + U(0xd00))
#define GICR_NSACR			(GICR_SGIBASE_OFFSET + U(0xe00))




#define IGROUPR_SHIFT		5
#define ISENABLER_SHIFT		5
#define ICENABLER_SHIFT		ISENABLER_SHIFT
#define ISPENDR_SHIFT		5
#define ICPENDR_SHIFT		ISPENDR_SHIFT
#define ISACTIVER_SHIFT		5
#define ICACTIVER_SHIFT		ISACTIVER_SHIFT
#define IPRIORITYR_SHIFT	2
#define ITARGETSR_SHIFT		2
#define ICFGR_SHIFT		4
#define NSACR_SHIFT		4





/* Constants to categorise interrupts */
#define MIN_SGI_ID		U(0)
#define MIN_SEC_SGI_ID		U(8)
#define MIN_PPI_ID		U(16)
#define MIN_SPI_ID		U(32)
#define MAX_SPI_ID		U(1019)



/*******************************************************************************
 * GIC Distributor interface register offsets that are common to GICv3 & GICv2
 ******************************************************************************/
#define GICD_CTLR		U(0x0)
#define GICD_TYPER		U(0x4)
#define GICD_IIDR		U(0x8)
#define GICD_IGROUPR		U(0x80)
#define GICD_ISENABLER		U(0x100)
#define GICD_ICENABLER		U(0x180)
#define GICD_ISPENDR		U(0x200)
#define GICD_ICPENDR		U(0x280)
#define GICD_ISACTIVER		U(0x300)
#define GICD_ICACTIVER		U(0x380)
#define GICD_IPRIORITYR		U(0x400)
#define GICD_ICFGR		U(0xc00)
#define GICD_NSACR		U(0xe00)

#define GICD_IROUTER		U(0x6000)

#define GICD_STATUSR		U(0x10)
#define GICD_SETSPI_NSR		U(0x40)
#define GICD_CLRSPI_NSR		U(0x48)
#define GICD_SETSPI_SR		U(0x50)
#define GICD_CLRSPI_SR		U(0x50)
#define GICD_IGRPMODR		U(0xd00)

#endif /* KBSPRAWINTR_H_ */
